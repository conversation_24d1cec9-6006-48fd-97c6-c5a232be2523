(function(p,Ge,ot,pe,Q,w,He,rt){"use strict";const me=class me extends pe{oninit(t){super.oninit(t),this.ButtonsCustomizationItemData=this.attrs.ButtonsCustomizationItemData,this.settingType="add",this.loading=!1,this.ButtonsCustomizationItemData?(this.settingType="edit",this.itemName=Q(this.ButtonsCustomizationItemData.name()),this.itemUrl=Q(this.ButtonsCustomizationItemData.url()),this.itemIcon=Q(this.ButtonsCustomizationItemData.icon()),this.itemColor=Q(this.ButtonsCustomizationItemData.color())):(this.itemName=Q(""),this.itemUrl=Q(""),this.itemIcon=Q(""),this.itemColor=Q(""))}className(){return"Modal--medium"}title(){return this.settingType==="add"?p.translator.trans("client1-buttons-customization.admin.settings.item-add"):p.translator.trans("client1-buttons-customization.admin.settings.item-edit")}content(){return w("div",{className:"Modal-body"},[w("div",{className:"Form"},[w("div",{className:"Form-group",style:"text-align: center;"},[w("div",[w("div",{className:"GuaGuaLeSettingsLabel"},p.translator.trans("client1-buttons-customization.admin.settings.item-name")),w("input",{maxlength:255,required:!0,className:"FormControl",value:this.itemName(),oninput:t=>this.itemName(t.target.value)}),w("div",{className:"GuaGuaLeSettingsLabel"},p.translator.trans("client1-buttons-customization.admin.settings.item-url")),w("input",{maxlength:500,required:!0,className:"FormControl",value:this.itemUrl(),oninput:t=>this.itemUrl(t.target.value)}),w("div",{className:"GuaGuaLeSettingsLabel"},p.translator.trans("client1-buttons-customization.admin.settings.item-icon")),w("input",{maxlength:50,required:!0,className:"FormControl",value:this.itemIcon(),oninput:t=>this.itemIcon(t.target.value)})])]),w("div",{className:"Form-group",style:"text-align: center;"},[w(ot,{className:"Button Button--primary",type:"submit",loading:this.loading},p.translator.trans("client1-buttons-customization.admin.confirm"))," ",w(ot,{className:"Button guagualeButton--gray",loading:this.loading,onclick:()=>this.hide()},p.translator.trans("client1-buttons-customization.admin.cancel"))])])])}onsubmit(t){t.preventDefault(),this.loading=!0,this.settingType==="edit"&&this.ButtonsCustomizationItemData?this.ButtonsCustomizationItemData.save({name:this.itemName(),url:this.itemUrl(),icon:this.itemIcon(),color:this.itemColor()}).then(()=>{this.attrs.onSave&&this.attrs.onSave(),this.hide()}).catch(e=>{this.loading=!1,this.handleErrors(e)}):p.store.createRecord("buttonsCustomizationList").save({name:this.itemName(),url:this.itemUrl(),icon:this.itemIcon(),color:this.itemColor()}).then(()=>{this.attrs.onSave&&this.attrs.onSave(),this.hide()}).catch(e=>{this.loading=!1,this.handleErrors(e)})}handleErrors(t){p.alerts.show({type:"error"},p.translator.trans("client1-buttons-customization.admin.save-error"))}};me.isDismissible=!1;let zt=me;const ge=class ge extends pe{oninit(t){super.oninit(t),this.ButtonsCustomizationItemData=this.attrs.ButtonsCustomizationItemData,this.loading=!1}className(){return"Modal--small"}title(){return p.translator.trans("client1-buttons-customization.admin.settings.item-delete-confirmation")}content(){return w("div",{className:"Modal-body"},[w("div",{className:"Form-group",style:"text-align: center;"},[w(ot,{className:"Button Button--primary",type:"submit",loading:this.loading},p.translator.trans("client1-buttons-customization.admin.confirm"))," ",w(ot,{className:"Button guagualeButton--gray",loading:this.loading,onclick:()=>this.hide()},p.translator.trans("client1-buttons-customization.admin.cancel"))])])}onsubmit(t){t.preventDefault(),this.loading=!0,this.ButtonsCustomizationItemData.delete().then(()=>{location.reload()})}};ge.isDismissible=!1;let Kt=ge;class We extends He{view(){const{ButtonsCustomizationItemData:t}=this.attrs,e=t.id(),n=t.name(),o=t.url(),r=t.icon();return w("div",{style:"border: 1px dotted var(--control-color);padding: 10px;border-radius: 4px;"},[w("div",[w("div",{style:"padding-top: 5px;"},[w(ot,{className:"Button Button--primary",onclick:()=>this.editItem(t)},p.translator.trans("client1-buttons-customization.admin.settings.item-edit"))," ",w(ot,{className:"Button Button--danger",style:"font-weight:bold;width:66px;",onclick:()=>this.deleteItem(t)},p.translator.trans("client1-buttons-customization.admin.settings.item-delete"))," ",w("b",p.translator.trans("client1-buttons-customization.admin.settings.item-id")+": "),e," | ",w("i",{className:r})," ",w("b",p.translator.trans("client1-buttons-customization.admin.settings.item-name")+": "),n," | ",w("b",p.translator.trans("client1-buttons-customization.admin.settings.item-url")+": "),o," "])])])}editItem(t){p.modal.show(zt,{ButtonsCustomizationItemData:t,onSave:this.attrs.onSave})}deleteItem(t){p.modal.show(Kt,{ButtonsCustomizationItemData:t})}}/**!
 * Sortable 1.15.6
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function ve(i,t){var e=Object.keys(i);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(i);t&&(n=n.filter(function(o){return Object.getOwnPropertyDescriptor(i,o).enumerable})),e.push.apply(e,n)}return e}function W(i){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?ve(Object(e),!0).forEach(function(n){je(i,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(e)):ve(Object(e)).forEach(function(n){Object.defineProperty(i,n,Object.getOwnPropertyDescriptor(e,n))})}return i}function Ft(i){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ft=function(t){return typeof t}:Ft=function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ft(i)}function je(i,t,e){return t in i?Object.defineProperty(i,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):i[t]=e,i}function $(){return $=Object.assign||function(i){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(i[n]=e[n])}return i},$.apply(this,arguments)}function Ue(i,t){if(i==null)return{};var e={},n=Object.keys(i),o,r;for(r=0;r<n.length;r++)o=n[r],!(t.indexOf(o)>=0)&&(e[o]=i[o]);return e}function $e(i,t){if(i==null)return{};var e=Ue(i,t),n,o;if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(i);for(o=0;o<r.length;o++)n=r[o],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(i,n)&&(e[n]=i[n])}return e}var qe="1.15.6";function q(i){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(i)}var V=q(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),bt=q(/Edge/i),be=q(/firefox/i),yt=q(/safari/i)&&!q(/chrome/i)&&!q(/android/i),Zt=q(/iP(ad|od|hone)/i),ye=q(/chrome/i)&&q(/android/i),we={capture:!1,passive:!1};function b(i,t,e){i.addEventListener(t,e,!V&&we)}function v(i,t,e){i.removeEventListener(t,e,!V&&we)}function Mt(i,t){if(t){if(t[0]===">"&&(t=t.substring(1)),i)try{if(i.matches)return i.matches(t);if(i.msMatchesSelector)return i.msMatchesSelector(t);if(i.webkitMatchesSelector)return i.webkitMatchesSelector(t)}catch{return!1}return!1}}function Ee(i){return i.host&&i!==document&&i.host.nodeType?i.host:i.parentNode}function X(i,t,e,n){if(i){e=e||document;do{if(t!=null&&(t[0]===">"?i.parentNode===e&&Mt(i,t):Mt(i,t))||n&&i===e)return i;if(i===e)break}while(i=Ee(i))}return null}var De=/\s+/g;function M(i,t,e){if(i&&t)if(i.classList)i.classList[e?"add":"remove"](t);else{var n=(" "+i.className+" ").replace(De," ").replace(" "+t+" "," ");i.className=(n+(e?" "+t:"")).replace(De," ")}}function h(i,t,e){var n=i&&i.style;if(n){if(e===void 0)return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(i,""):i.currentStyle&&(e=i.currentStyle),t===void 0?e:e[t];!(t in n)&&t.indexOf("webkit")===-1&&(t="-webkit-"+t),n[t]=e+(typeof e=="string"?"":"px")}}function ft(i,t){var e="";if(typeof i=="string")e=i;else do{var n=h(i,"transform");n&&n!=="none"&&(e=n+" "+e)}while(!t&&(i=i.parentNode));var o=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return o&&new o(e)}function _e(i,t,e){if(i){var n=i.getElementsByTagName(t),o=0,r=n.length;if(e)for(;o<r;o++)e(n[o],o);return n}return[]}function j(){var i=document.scrollingElement;return i||document.documentElement}function I(i,t,e,n,o){if(!(!i.getBoundingClientRect&&i!==window)){var r,a,s,l,u,f,d;if(i!==window&&i.parentNode&&i!==j()?(r=i.getBoundingClientRect(),a=r.top,s=r.left,l=r.bottom,u=r.right,f=r.height,d=r.width):(a=0,s=0,l=window.innerHeight,u=window.innerWidth,f=window.innerHeight,d=window.innerWidth),(t||e)&&i!==window&&(o=o||i.parentNode,!V))do if(o&&o.getBoundingClientRect&&(h(o,"transform")!=="none"||e&&h(o,"position")!=="static")){var y=o.getBoundingClientRect();a-=y.top+parseInt(h(o,"border-top-width")),s-=y.left+parseInt(h(o,"border-left-width")),l=a+r.height,u=s+r.width;break}while(o=o.parentNode);if(n&&i!==window){var _=ft(o||i),E=_&&_.a,D=_&&_.d;_&&(a/=D,s/=E,d/=E,f/=D,l=a+f,u=s+d)}return{top:a,left:s,bottom:l,right:u,width:d,height:f}}}function Se(i,t,e){for(var n=J(i,!0),o=I(i)[t];n;){var r=I(n)[e],a=void 0;if(a=o>=r,!a)return n;if(n===j())break;n=J(n,!1)}return!1}function ht(i,t,e,n){for(var o=0,r=0,a=i.children;r<a.length;){if(a[r].style.display!=="none"&&a[r]!==m.ghost&&(n||a[r]!==m.dragged)&&X(a[r],e.draggable,i,!1)){if(o===t)return a[r];o++}r++}return null}function Qt(i,t){for(var e=i.lastElementChild;e&&(e===m.ghost||h(e,"display")==="none"||t&&!Mt(e,t));)e=e.previousElementSibling;return e||null}function L(i,t){var e=0;if(!i||!i.parentNode)return-1;for(;i=i.previousElementSibling;)i.nodeName.toUpperCase()!=="TEMPLATE"&&i!==m.clone&&(!t||Mt(i,t))&&e++;return e}function Ce(i){var t=0,e=0,n=j();if(i)do{var o=ft(i),r=o.a,a=o.d;t+=i.scrollLeft*r,e+=i.scrollTop*a}while(i!==n&&(i=i.parentNode));return[t,e]}function Ve(i,t){for(var e in i)if(i.hasOwnProperty(e)){for(var n in t)if(t.hasOwnProperty(n)&&t[n]===i[e][n])return Number(e)}return-1}function J(i,t){if(!i||!i.getBoundingClientRect)return j();var e=i,n=!1;do if(e.clientWidth<e.scrollWidth||e.clientHeight<e.scrollHeight){var o=h(e);if(e.clientWidth<e.scrollWidth&&(o.overflowX=="auto"||o.overflowX=="scroll")||e.clientHeight<e.scrollHeight&&(o.overflowY=="auto"||o.overflowY=="scroll")){if(!e.getBoundingClientRect||e===document.body)return j();if(n||t)return e;n=!0}}while(e=e.parentNode);return j()}function Ke(i,t){if(i&&t)for(var e in t)t.hasOwnProperty(e)&&(i[e]=t[e]);return i}function Jt(i,t){return Math.round(i.top)===Math.round(t.top)&&Math.round(i.left)===Math.round(t.left)&&Math.round(i.height)===Math.round(t.height)&&Math.round(i.width)===Math.round(t.width)}var wt;function Te(i,t){return function(){if(!wt){var e=arguments,n=this;e.length===1?i.call(n,e[0]):i.apply(n,e),wt=setTimeout(function(){wt=void 0},t)}}}function Ze(){clearTimeout(wt),wt=void 0}function Ie(i,t,e){i.scrollLeft+=t,i.scrollTop+=e}function Ne(i){var t=window.Polymer,e=window.jQuery||window.Zepto;return t&&t.dom?t.dom(i).cloneNode(!0):e?e(i).clone(!0)[0]:i.cloneNode(!0)}function Oe(i,t,e){var n={};return Array.from(i.children).forEach(function(o){var r,a,s,l;if(!(!X(o,t.draggable,i,!1)||o.animated||o===e)){var u=I(o);n.left=Math.min((r=n.left)!==null&&r!==void 0?r:1/0,u.left),n.top=Math.min((a=n.top)!==null&&a!==void 0?a:1/0,u.top),n.right=Math.max((s=n.right)!==null&&s!==void 0?s:-1/0,u.right),n.bottom=Math.max((l=n.bottom)!==null&&l!==void 0?l:-1/0,u.bottom)}}),n.width=n.right-n.left,n.height=n.bottom-n.top,n.x=n.left,n.y=n.top,n}var z="Sortable"+new Date().getTime();function Qe(){var i=[],t;return{captureAnimationState:function(){if(i=[],!!this.options.animation){var n=[].slice.call(this.el.children);n.forEach(function(o){if(!(h(o,"display")==="none"||o===m.ghost)){i.push({target:o,rect:I(o)});var r=W({},i[i.length-1].rect);if(o.thisAnimationDuration){var a=ft(o,!0);a&&(r.top-=a.f,r.left-=a.e)}o.fromRect=r}})}},addAnimationState:function(n){i.push(n)},removeAnimationState:function(n){i.splice(Ve(i,{target:n}),1)},animateAll:function(n){var o=this;if(!this.options.animation){clearTimeout(t),typeof n=="function"&&n();return}var r=!1,a=0;i.forEach(function(s){var l=0,u=s.target,f=u.fromRect,d=I(u),y=u.prevFromRect,_=u.prevToRect,E=s.rect,D=ft(u,!0);D&&(d.top-=D.f,d.left-=D.e),u.toRect=d,u.thisAnimationDuration&&Jt(y,d)&&!Jt(f,d)&&(E.top-d.top)/(E.left-d.left)===(f.top-d.top)/(f.left-d.left)&&(l=tn(E,y,_,o.options)),Jt(d,f)||(u.prevFromRect=f,u.prevToRect=d,l||(l=o.options.animation),o.animate(u,E,d,l)),l&&(r=!0,a=Math.max(a,l),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},l),u.thisAnimationDuration=l)}),clearTimeout(t),r?t=setTimeout(function(){typeof n=="function"&&n()},a):typeof n=="function"&&n(),i=[]},animate:function(n,o,r,a){if(a){h(n,"transition",""),h(n,"transform","");var s=ft(this.el),l=s&&s.a,u=s&&s.d,f=(o.left-r.left)/(l||1),d=(o.top-r.top)/(u||1);n.animatingX=!!f,n.animatingY=!!d,h(n,"transform","translate3d("+f+"px,"+d+"px,0)"),this.forRepaintDummy=Je(n),h(n,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(n,"transform","translate3d(0,0,0)"),typeof n.animated=="number"&&clearTimeout(n.animated),n.animated=setTimeout(function(){h(n,"transition",""),h(n,"transform",""),n.animated=!1,n.animatingX=!1,n.animatingY=!1},a)}}}}function Je(i){return i.offsetWidth}function tn(i,t,e,n){return Math.sqrt(Math.pow(t.top-i.top,2)+Math.pow(t.left-i.left,2))/Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))*n.animation}var mt=[],te={initializeByDefault:!0},Et={mount:function(t){for(var e in te)te.hasOwnProperty(e)&&!(e in t)&&(t[e]=te[e]);mt.forEach(function(n){if(n.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")}),mt.push(t)},pluginEvent:function(t,e,n){var o=this;this.eventCanceled=!1,n.cancel=function(){o.eventCanceled=!0};var r=t+"Global";mt.forEach(function(a){e[a.pluginName]&&(e[a.pluginName][r]&&e[a.pluginName][r](W({sortable:e},n)),e.options[a.pluginName]&&e[a.pluginName][t]&&e[a.pluginName][t](W({sortable:e},n)))})},initializePlugins:function(t,e,n,o){mt.forEach(function(s){var l=s.pluginName;if(!(!t.options[l]&&!s.initializeByDefault)){var u=new s(t,e,t.options);u.sortable=t,u.options=t.options,t[l]=u,$(n,u.defaults)}});for(var r in t.options)if(t.options.hasOwnProperty(r)){var a=this.modifyOption(t,r,t.options[r]);typeof a<"u"&&(t.options[r]=a)}},getEventProperties:function(t,e){var n={};return mt.forEach(function(o){typeof o.eventProperties=="function"&&$(n,o.eventProperties.call(e[o.pluginName],t))}),n},modifyOption:function(t,e,n){var o;return mt.forEach(function(r){t[r.pluginName]&&r.optionListeners&&typeof r.optionListeners[e]=="function"&&(o=r.optionListeners[e].call(t[r.pluginName],n))}),o}};function en(i){var t=i.sortable,e=i.rootEl,n=i.name,o=i.targetEl,r=i.cloneEl,a=i.toEl,s=i.fromEl,l=i.oldIndex,u=i.newIndex,f=i.oldDraggableIndex,d=i.newDraggableIndex,y=i.originalEvent,_=i.putSortable,E=i.extraEventProperties;if(t=t||e&&e[z],!!t){var D,G=t.options,K="on"+n.charAt(0).toUpperCase()+n.substr(1);window.CustomEvent&&!V&&!bt?D=new CustomEvent(n,{bubbles:!0,cancelable:!0}):(D=document.createEvent("Event"),D.initEvent(n,!0,!0)),D.to=a||e,D.from=s||e,D.item=o||e,D.clone=r,D.oldIndex=l,D.newIndex=u,D.oldDraggableIndex=f,D.newDraggableIndex=d,D.originalEvent=y,D.pullMode=_?_.lastPutMode:void 0;var A=W(W({},E),Et.getEventProperties(n,t));for(var H in A)D[H]=A[H];e&&e.dispatchEvent(D),G[K]&&G[K].call(t,D)}}var nn=["evt"],F=function(t,e){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=n.evt,r=$e(n,nn);Et.pluginEvent.bind(m)(t,e,W({dragEl:c,parentEl:T,ghostEl:g,rootEl:S,nextEl:at,lastDownEl:Bt,cloneEl:C,cloneHidden:tt,dragStarted:_t,putSortable:O,activeSortable:m.active,originalEvent:o,oldIndex:gt,oldDraggableIndex:Dt,newIndex:B,newDraggableIndex:et,hideGhostForTarget:Be,unhideGhostForTarget:Re,cloneNowHidden:function(){tt=!0},cloneNowShown:function(){tt=!1},dispatchSortableEvent:function(s){P({sortable:e,name:s,originalEvent:o})}},r))};function P(i){en(W({putSortable:O,cloneEl:C,targetEl:c,rootEl:S,oldIndex:gt,oldDraggableIndex:Dt,newIndex:B,newDraggableIndex:et},i))}var c,T,g,S,at,Bt,C,tt,gt,B,Dt,et,Rt,O,pt=!1,kt=!1,Lt=[],st,Y,ee,ne,xe,Pe,_t,vt,St,Ct=!1,Xt=!1,Yt,x,ie=[],oe=!1,Gt=[],Ht=typeof document<"u",Wt=Zt,Ae=bt||V?"cssFloat":"float",on=Ht&&!ye&&!Zt&&"draggable"in document.createElement("div"),ze=(function(){if(Ht){if(V)return!1;var i=document.createElement("x");return i.style.cssText="pointer-events:auto",i.style.pointerEvents==="auto"}})(),Fe=function(t,e){var n=h(t),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),r=ht(t,0,e),a=ht(t,1,e),s=r&&h(r),l=a&&h(a),u=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+I(r).width,f=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+I(a).width;if(n.display==="flex")return n.flexDirection==="column"||n.flexDirection==="column-reverse"?"vertical":"horizontal";if(n.display==="grid")return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&s.float&&s.float!=="none"){var d=s.float==="left"?"left":"right";return a&&(l.clear==="both"||l.clear===d)?"vertical":"horizontal"}return r&&(s.display==="block"||s.display==="flex"||s.display==="table"||s.display==="grid"||u>=o&&n[Ae]==="none"||a&&n[Ae]==="none"&&u+f>o)?"vertical":"horizontal"},rn=function(t,e,n){var o=n?t.left:t.top,r=n?t.right:t.bottom,a=n?t.width:t.height,s=n?e.left:e.top,l=n?e.right:e.bottom,u=n?e.width:e.height;return o===s||r===l||o+a/2===s+u/2},an=function(t,e){var n;return Lt.some(function(o){var r=o[z].options.emptyInsertThreshold;if(!(!r||Qt(o))){var a=I(o),s=t>=a.left-r&&t<=a.right+r,l=e>=a.top-r&&e<=a.bottom+r;if(s&&l)return n=o}}),n},Me=function(t){function e(r,a){return function(s,l,u,f){var d=s.options.group.name&&l.options.group.name&&s.options.group.name===l.options.group.name;if(r==null&&(a||d))return!0;if(r==null||r===!1)return!1;if(a&&r==="clone")return r;if(typeof r=="function")return e(r(s,l,u,f),a)(s,l,u,f);var y=(a?s:l).options.group.name;return r===!0||typeof r=="string"&&r===y||r.join&&r.indexOf(y)>-1}}var n={},o=t.group;(!o||Ft(o)!="object")&&(o={name:o}),n.name=o.name,n.checkPull=e(o.pull,!0),n.checkPut=e(o.put),n.revertClone=o.revertClone,t.group=n},Be=function(){!ze&&g&&h(g,"display","none")},Re=function(){!ze&&g&&h(g,"display","")};Ht&&!ye&&document.addEventListener("click",function(i){if(kt)return i.preventDefault(),i.stopPropagation&&i.stopPropagation(),i.stopImmediatePropagation&&i.stopImmediatePropagation(),kt=!1,!1},!0);var lt=function(t){if(c){t=t.touches?t.touches[0]:t;var e=an(t.clientX,t.clientY);if(e){var n={};for(var o in t)t.hasOwnProperty(o)&&(n[o]=t[o]);n.target=n.rootEl=e,n.preventDefault=void 0,n.stopPropagation=void 0,e[z]._onDragOver(n)}}},sn=function(t){c&&c.parentNode[z]._isOutsideThisEl(t.target)};function m(i,t){if(!(i&&i.nodeType&&i.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(i));this.el=i,this.options=t=$({},t),i[z]=this;var e={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(i.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Fe(i,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,s){a.setData("Text",s.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:m.supportPointer!==!1&&"PointerEvent"in window&&(!yt||Zt),emptyInsertThreshold:5};Et.initializePlugins(this,i,e);for(var n in e)!(n in t)&&(t[n]=e[n]);Me(t);for(var o in this)o.charAt(0)==="_"&&typeof this[o]=="function"&&(this[o]=this[o].bind(this));this.nativeDraggable=t.forceFallback?!1:on,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?b(i,"pointerdown",this._onTapStart):(b(i,"mousedown",this._onTapStart),b(i,"touchstart",this._onTapStart)),this.nativeDraggable&&(b(i,"dragover",this),b(i,"dragenter",this)),Lt.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),$(this,Qe())}m.prototype={constructor:m,_isOutsideThisEl:function(t){!this.el.contains(t)&&t!==this.el&&(vt=null)},_getDirection:function(t,e){return typeof this.options.direction=="function"?this.options.direction.call(this,t,e,c):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,n=this.el,o=this.options,r=o.preventOnFilter,a=t.type,s=t.touches&&t.touches[0]||t.pointerType&&t.pointerType==="touch"&&t,l=(s||t).target,u=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||l,f=o.filter;if(gn(n),!c&&!(/mousedown|pointerdown/.test(a)&&t.button!==0||o.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&yt&&l&&l.tagName.toUpperCase()==="SELECT")&&(l=X(l,o.draggable,n,!1),!(l&&l.animated)&&Bt!==l)){if(gt=L(l),Dt=L(l,o.draggable),typeof f=="function"){if(f.call(this,t,l,this)){P({sortable:e,rootEl:u,name:"filter",targetEl:l,toEl:n,fromEl:n}),F("filter",e,{evt:t}),r&&t.preventDefault();return}}else if(f&&(f=f.split(",").some(function(d){if(d=X(u,d.trim(),n,!1),d)return P({sortable:e,rootEl:d,name:"filter",targetEl:l,fromEl:n,toEl:n}),F("filter",e,{evt:t}),!0}),f)){r&&t.preventDefault();return}o.handle&&!X(u,o.handle,n,!1)||this._prepareDragStart(t,s,l)}}},_prepareDragStart:function(t,e,n){var o=this,r=o.el,a=o.options,s=r.ownerDocument,l;if(n&&!c&&n.parentNode===r){var u=I(n);if(S=r,c=n,T=c.parentNode,at=c.nextSibling,Bt=n,Rt=a.group,m.dragged=c,st={target:c,clientX:(e||t).clientX,clientY:(e||t).clientY},xe=st.clientX-u.left,Pe=st.clientY-u.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,c.style["will-change"]="all",l=function(){if(F("delayEnded",o,{evt:t}),m.eventCanceled){o._onDrop();return}o._disableDelayedDragEvents(),!be&&o.nativeDraggable&&(c.draggable=!0),o._triggerDragStart(t,e),P({sortable:o,name:"choose",originalEvent:t}),M(c,a.chosenClass,!0)},a.ignore.split(",").forEach(function(f){_e(c,f.trim(),re)}),b(s,"dragover",lt),b(s,"mousemove",lt),b(s,"touchmove",lt),a.supportPointer?(b(s,"pointerup",o._onDrop),!this.nativeDraggable&&b(s,"pointercancel",o._onDrop)):(b(s,"mouseup",o._onDrop),b(s,"touchend",o._onDrop),b(s,"touchcancel",o._onDrop)),be&&this.nativeDraggable&&(this.options.touchStartThreshold=4,c.draggable=!0),F("delayStart",this,{evt:t}),a.delay&&(!a.delayOnTouchOnly||e)&&(!this.nativeDraggable||!(bt||V))){if(m.eventCanceled){this._onDrop();return}a.supportPointer?(b(s,"pointerup",o._disableDelayedDrag),b(s,"pointercancel",o._disableDelayedDrag)):(b(s,"mouseup",o._disableDelayedDrag),b(s,"touchend",o._disableDelayedDrag),b(s,"touchcancel",o._disableDelayedDrag)),b(s,"mousemove",o._delayedDragTouchMoveHandler),b(s,"touchmove",o._delayedDragTouchMoveHandler),a.supportPointer&&b(s,"pointermove",o._delayedDragTouchMoveHandler),o._dragStartTimer=setTimeout(l,a.delay)}else l()}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){c&&re(c),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;v(t,"mouseup",this._disableDelayedDrag),v(t,"touchend",this._disableDelayedDrag),v(t,"touchcancel",this._disableDelayedDrag),v(t,"pointerup",this._disableDelayedDrag),v(t,"pointercancel",this._disableDelayedDrag),v(t,"mousemove",this._delayedDragTouchMoveHandler),v(t,"touchmove",this._delayedDragTouchMoveHandler),v(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||t.pointerType=="touch"&&t,!this.nativeDraggable||e?this.options.supportPointer?b(document,"pointermove",this._onTouchMove):e?b(document,"touchmove",this._onTouchMove):b(document,"mousemove",this._onTouchMove):(b(c,"dragend",this),b(S,"dragstart",this._onDragStart));try{document.selection?Ut(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(t,e){if(pt=!1,S&&c){F("dragStarted",this,{evt:e}),this.nativeDraggable&&b(document,"dragover",sn);var n=this.options;!t&&M(c,n.dragClass,!1),M(c,n.ghostClass,!0),m.active=this,t&&this._appendGhost(),P({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(Y){this._lastX=Y.clientX,this._lastY=Y.clientY,Be();for(var t=document.elementFromPoint(Y.clientX,Y.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(Y.clientX,Y.clientY),t!==e);)e=t;if(c.parentNode[z]._isOutsideThisEl(t),e)do{if(e[z]){var n=void 0;if(n=e[z]._onDragOver({clientX:Y.clientX,clientY:Y.clientY,target:t,rootEl:e}),n&&!this.options.dragoverBubble)break}t=e}while(e=Ee(e));Re()}},_onTouchMove:function(t){if(st){var e=this.options,n=e.fallbackTolerance,o=e.fallbackOffset,r=t.touches?t.touches[0]:t,a=g&&ft(g,!0),s=g&&a&&a.a,l=g&&a&&a.d,u=Wt&&x&&Ce(x),f=(r.clientX-st.clientX+o.x)/(s||1)+(u?u[0]-ie[0]:0)/(s||1),d=(r.clientY-st.clientY+o.y)/(l||1)+(u?u[1]-ie[1]:0)/(l||1);if(!m.active&&!pt){if(n&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}if(g){a?(a.e+=f-(ee||0),a.f+=d-(ne||0)):a={a:1,b:0,c:0,d:1,e:f,f:d};var y="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(g,"webkitTransform",y),h(g,"mozTransform",y),h(g,"msTransform",y),h(g,"transform",y),ee=f,ne=d,Y=r}t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!g){var t=this.options.fallbackOnBody?document.body:S,e=I(c,!0,Wt,!0,t),n=this.options;if(Wt){for(x=t;h(x,"position")==="static"&&h(x,"transform")==="none"&&x!==document;)x=x.parentNode;x!==document.body&&x!==document.documentElement?(x===document&&(x=j()),e.top+=x.scrollTop,e.left+=x.scrollLeft):x=j(),ie=Ce(x)}g=c.cloneNode(!0),M(g,n.ghostClass,!1),M(g,n.fallbackClass,!0),M(g,n.dragClass,!0),h(g,"transition",""),h(g,"transform",""),h(g,"box-sizing","border-box"),h(g,"margin",0),h(g,"top",e.top),h(g,"left",e.left),h(g,"width",e.width),h(g,"height",e.height),h(g,"opacity","0.8"),h(g,"position",Wt?"absolute":"fixed"),h(g,"zIndex","100000"),h(g,"pointerEvents","none"),m.ghost=g,t.appendChild(g),h(g,"transform-origin",xe/parseInt(g.style.width)*100+"% "+Pe/parseInt(g.style.height)*100+"%")}},_onDragStart:function(t,e){var n=this,o=t.dataTransfer,r=n.options;if(F("dragStart",this,{evt:t}),m.eventCanceled){this._onDrop();return}F("setupClone",this),m.eventCanceled||(C=Ne(c),C.removeAttribute("id"),C.draggable=!1,C.style["will-change"]="",this._hideClone(),M(C,this.options.chosenClass,!1),m.clone=C),n.cloneId=Ut(function(){F("clone",n),!m.eventCanceled&&(n.options.removeCloneOnHide||S.insertBefore(C,c),n._hideClone(),P({sortable:n,name:"clone"}))}),!e&&M(c,r.dragClass,!0),e?(kt=!0,n._loopId=setInterval(n._emulateDragOver,50)):(v(document,"mouseup",n._onDrop),v(document,"touchend",n._onDrop),v(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",r.setData&&r.setData.call(n,o,c)),b(document,"drop",n),h(c,"transform","translateZ(0)")),pt=!0,n._dragStartId=Ut(n._dragStarted.bind(n,e,t)),b(document,"selectstart",n),_t=!0,window.getSelection().removeAllRanges(),yt&&h(document.body,"user-select","none")},_onDragOver:function(t){var e=this.el,n=t.target,o,r,a,s=this.options,l=s.group,u=m.active,f=Rt===l,d=s.sort,y=O||u,_,E=this,D=!1;if(oe)return;function G(At,bn){F(At,E,W({evt:t,isOwner:f,axis:_?"vertical":"horizontal",revert:a,dragRect:o,targetRect:r,canSort:d,fromSortable:y,target:n,completed:A,onMove:function(Ye,yn){return jt(S,e,c,o,Ye,I(Ye),t,yn)},changed:H},bn))}function K(){G("dragOverAnimationCapture"),E.captureAnimationState(),E!==y&&y.captureAnimationState()}function A(At){return G("dragOverCompleted",{insertion:At}),At&&(f?u._hideClone():u._showClone(E),E!==y&&(M(c,O?O.options.ghostClass:u.options.ghostClass,!1),M(c,s.ghostClass,!0)),O!==E&&E!==m.active?O=E:E===m.active&&O&&(O=null),y===E&&(E._ignoreWhileAnimating=n),E.animateAll(function(){G("dragOverAnimationComplete"),E._ignoreWhileAnimating=null}),E!==y&&(y.animateAll(),y._ignoreWhileAnimating=null)),(n===c&&!c.animated||n===e&&!n.animated)&&(vt=null),!s.dragoverBubble&&!t.rootEl&&n!==document&&(c.parentNode[z]._isOutsideThisEl(t.target),!At&&lt(t)),!s.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),D=!0}function H(){B=L(c),et=L(c,s.draggable),P({sortable:E,name:"change",toEl:e,newIndex:B,newDraggableIndex:et,originalEvent:t})}if(t.preventDefault!==void 0&&t.cancelable&&t.preventDefault(),n=X(n,s.draggable,e,!0),G("dragOver"),m.eventCanceled)return D;if(c.contains(t.target)||n.animated&&n.animatingX&&n.animatingY||E._ignoreWhileAnimating===n)return A(!1);if(kt=!1,u&&!s.disabled&&(f?d||(a=T!==S):O===this||(this.lastPutMode=Rt.checkPull(this,u,c,t))&&l.checkPut(this,u,c,t))){if(_=this._getDirection(t,n)==="vertical",o=I(c),G("dragOverValid"),m.eventCanceled)return D;if(a)return T=S,K(),this._hideClone(),G("revert"),m.eventCanceled||(at?S.insertBefore(c,at):S.appendChild(c)),A(!0);var R=Qt(e,s.draggable);if(!R||dn(t,_,this)&&!R.animated){if(R===c)return A(!1);if(R&&e===t.target&&(n=R),n&&(r=I(n)),jt(S,e,c,o,n,r,t,!!n)!==!1)return K(),R&&R.nextSibling?e.insertBefore(c,R.nextSibling):e.appendChild(c),T=e,H(),A(!0)}else if(R&&cn(t,_,this)){var ut=ht(e,0,s,!0);if(ut===c)return A(!1);if(n=ut,r=I(n),jt(S,e,c,o,n,r,t,!1)!==!1)return K(),e.insertBefore(c,ut),T=e,H(),A(!0)}else if(n.parentNode===e){r=I(n);var U=0,ct,Nt=c.parentNode!==e,k=!rn(c.animated&&c.toRect||o,n.animated&&n.toRect||r,_),Ot=_?"top":"left",nt=Se(n,"top","top")||Se(c,"top","top"),xt=nt?nt.scrollTop:void 0;vt!==n&&(ct=r[Ot],Ct=!1,Xt=!k&&s.invertSwap||Nt),U=fn(t,n,r,_,k?1:s.swapThreshold,s.invertedSwapThreshold==null?s.swapThreshold:s.invertedSwapThreshold,Xt,vt===n);var Z;if(U!==0){var dt=L(c);do dt-=U,Z=T.children[dt];while(Z&&(h(Z,"display")==="none"||Z===g))}if(U===0||Z===n)return A(!1);vt=n,St=U;var Pt=n.nextElementSibling,it=!1;it=U===1;var Vt=jt(S,e,c,o,n,r,t,it);if(Vt!==!1)return(Vt===1||Vt===-1)&&(it=Vt===1),oe=!0,setTimeout(un,30),K(),it&&!Pt?e.appendChild(c):n.parentNode.insertBefore(c,it?Pt:n),nt&&Ie(nt,0,xt-nt.scrollTop),T=c.parentNode,ct!==void 0&&!Xt&&(Yt=Math.abs(ct-I(n)[Ot])),H(),A(!0)}if(e.contains(c))return A(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){v(document,"mousemove",this._onTouchMove),v(document,"touchmove",this._onTouchMove),v(document,"pointermove",this._onTouchMove),v(document,"dragover",lt),v(document,"mousemove",lt),v(document,"touchmove",lt)},_offUpEvents:function(){var t=this.el.ownerDocument;v(t,"mouseup",this._onDrop),v(t,"touchend",this._onDrop),v(t,"pointerup",this._onDrop),v(t,"pointercancel",this._onDrop),v(t,"touchcancel",this._onDrop),v(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;if(B=L(c),et=L(c,n.draggable),F("drop",this,{evt:t}),T=c&&c.parentNode,B=L(c),et=L(c,n.draggable),m.eventCanceled){this._nulling();return}pt=!1,Xt=!1,Ct=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),ae(this.cloneId),ae(this._dragStartId),this.nativeDraggable&&(v(document,"drop",this),v(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),yt&&h(document.body,"user-select",""),h(c,"transform",""),t&&(_t&&(t.cancelable&&t.preventDefault(),!n.dropBubble&&t.stopPropagation()),g&&g.parentNode&&g.parentNode.removeChild(g),(S===T||O&&O.lastPutMode!=="clone")&&C&&C.parentNode&&C.parentNode.removeChild(C),c&&(this.nativeDraggable&&v(c,"dragend",this),re(c),c.style["will-change"]="",_t&&!pt&&M(c,O?O.options.ghostClass:this.options.ghostClass,!1),M(c,this.options.chosenClass,!1),P({sortable:this,name:"unchoose",toEl:T,newIndex:null,newDraggableIndex:null,originalEvent:t}),S!==T?(B>=0&&(P({rootEl:T,name:"add",toEl:T,fromEl:S,originalEvent:t}),P({sortable:this,name:"remove",toEl:T,originalEvent:t}),P({rootEl:T,name:"sort",toEl:T,fromEl:S,originalEvent:t}),P({sortable:this,name:"sort",toEl:T,originalEvent:t})),O&&O.save()):B!==gt&&B>=0&&(P({sortable:this,name:"update",toEl:T,originalEvent:t}),P({sortable:this,name:"sort",toEl:T,originalEvent:t})),m.active&&((B==null||B===-1)&&(B=gt,et=Dt),P({sortable:this,name:"end",toEl:T,originalEvent:t}),this.save()))),this._nulling()},_nulling:function(){F("nulling",this),S=c=T=g=at=C=Bt=tt=st=Y=_t=B=et=gt=Dt=vt=St=O=Rt=m.dragged=m.ghost=m.clone=m.active=null,Gt.forEach(function(t){t.checked=!0}),Gt.length=ee=ne=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":c&&(this._onDragOver(t),ln(t));break;case"selectstart":t.preventDefault();break}},toArray:function(){for(var t=[],e,n=this.el.children,o=0,r=n.length,a=this.options;o<r;o++)e=n[o],X(e,a.draggable,this.el,!1)&&t.push(e.getAttribute(a.dataIdAttr)||mn(e));return t},sort:function(t,e){var n={},o=this.el;this.toArray().forEach(function(r,a){var s=o.children[a];X(s,this.options.draggable,o,!1)&&(n[r]=s)},this),e&&this.captureAnimationState(),t.forEach(function(r){n[r]&&(o.removeChild(n[r]),o.appendChild(n[r]))}),e&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return X(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(e===void 0)return n[t];var o=Et.modifyOption(this,t,e);typeof o<"u"?n[t]=o:n[t]=e,t==="group"&&Me(n)},destroy:function(){F("destroy",this);var t=this.el;t[z]=null,v(t,"mousedown",this._onTapStart),v(t,"touchstart",this._onTapStart),v(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(v(t,"dragover",this),v(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(e){e.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Lt.splice(Lt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!tt){if(F("hideClone",this),m.eventCanceled)return;h(C,"display","none"),this.options.removeCloneOnHide&&C.parentNode&&C.parentNode.removeChild(C),tt=!0}},_showClone:function(t){if(t.lastPutMode!=="clone"){this._hideClone();return}if(tt){if(F("showClone",this),m.eventCanceled)return;c.parentNode==S&&!this.options.group.revertClone?S.insertBefore(C,c):at?S.insertBefore(C,at):S.appendChild(C),this.options.group.revertClone&&this.animate(c,C),h(C,"display",""),tt=!1}}};function ln(i){i.dataTransfer&&(i.dataTransfer.dropEffect="move"),i.cancelable&&i.preventDefault()}function jt(i,t,e,n,o,r,a,s){var l,u=i[z],f=u.options.onMove,d;return window.CustomEvent&&!V&&!bt?l=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(l=document.createEvent("Event"),l.initEvent("move",!0,!0)),l.to=t,l.from=i,l.dragged=e,l.draggedRect=n,l.related=o||t,l.relatedRect=r||I(t),l.willInsertAfter=s,l.originalEvent=a,i.dispatchEvent(l),f&&(d=f.call(u,l,a)),d}function re(i){i.draggable=!1}function un(){oe=!1}function cn(i,t,e){var n=I(ht(e.el,0,e.options,!0)),o=Oe(e.el,e.options,g),r=10;return t?i.clientX<o.left-r||i.clientY<n.top&&i.clientX<n.right:i.clientY<o.top-r||i.clientY<n.bottom&&i.clientX<n.left}function dn(i,t,e){var n=I(Qt(e.el,e.options.draggable)),o=Oe(e.el,e.options,g),r=10;return t?i.clientX>o.right+r||i.clientY>n.bottom&&i.clientX>n.left:i.clientY>o.bottom+r||i.clientX>n.right&&i.clientY>n.top}function fn(i,t,e,n,o,r,a,s){var l=n?i.clientY:i.clientX,u=n?e.height:e.width,f=n?e.top:e.left,d=n?e.bottom:e.right,y=!1;if(!a){if(s&&Yt<u*o){if(!Ct&&(St===1?l>f+u*r/2:l<d-u*r/2)&&(Ct=!0),Ct)y=!0;else if(St===1?l<f+Yt:l>d-Yt)return-St}else if(l>f+u*(1-o)/2&&l<d-u*(1-o)/2)return hn(t)}return y=y||a,y&&(l<f+u*r/2||l>d-u*r/2)?l>f+u/2?1:-1:0}function hn(i){return L(c)<L(i)?1:-1}function mn(i){for(var t=i.tagName+i.className+i.src+i.href+i.textContent,e=t.length,n=0;e--;)n+=t.charCodeAt(e);return n.toString(36)}function gn(i){Gt.length=0;for(var t=i.getElementsByTagName("input"),e=t.length;e--;){var n=t[e];n.checked&&Gt.push(n)}}function Ut(i){return setTimeout(i,0)}function ae(i){return clearTimeout(i)}Ht&&b(document,"touchmove",function(i){(m.active||pt)&&i.cancelable&&i.preventDefault()}),m.utils={on:b,off:v,css:h,find:_e,is:function(t,e){return!!X(t,e,t,!1)},extend:Ke,throttle:Te,closest:X,toggleClass:M,clone:Ne,index:L,nextTick:Ut,cancelNextTick:ae,detectDirection:Fe,getChild:ht,expando:z},m.get=function(i){return i[z]},m.mount=function(){for(var i=arguments.length,t=new Array(i),e=0;e<i;e++)t[e]=arguments[e];t[0].constructor===Array&&(t=t[0]),t.forEach(function(n){if(!n.prototype||!n.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(n));n.utils&&(m.utils=W(W({},m.utils),n.utils)),Et.mount(n)})},m.create=function(i,t){return new m(i,t)},m.version=qe;var N=[],Tt,se,le=!1,ue,ce,$t,It;function pn(){function i(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var t in this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return i.prototype={dragStarted:function(e){var n=e.originalEvent;this.sortable.nativeDraggable?b(document,"dragover",this._handleAutoScroll):this.options.supportPointer?b(document,"pointermove",this._handleFallbackAutoScroll):n.touches?b(document,"touchmove",this._handleFallbackAutoScroll):b(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var n=e.originalEvent;!this.options.dragOverBubble&&!n.rootEl&&this._handleAutoScroll(n)},drop:function(){this.sortable.nativeDraggable?v(document,"dragover",this._handleAutoScroll):(v(document,"pointermove",this._handleFallbackAutoScroll),v(document,"touchmove",this._handleFallbackAutoScroll),v(document,"mousemove",this._handleFallbackAutoScroll)),ke(),qt(),Ze()},nulling:function(){$t=se=Tt=le=It=ue=ce=null,N.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,n){var o=this,r=(e.touches?e.touches[0]:e).clientX,a=(e.touches?e.touches[0]:e).clientY,s=document.elementFromPoint(r,a);if($t=e,n||this.options.forceAutoScrollFallback||bt||V||yt){de(e,this.options,s,n);var l=J(s,!0);le&&(!It||r!==ue||a!==ce)&&(It&&ke(),It=setInterval(function(){var u=J(document.elementFromPoint(r,a),!0);u!==l&&(l=u,qt()),de(e,o.options,u,n)},10),ue=r,ce=a)}else{if(!this.options.bubbleScroll||J(s,!0)===j()){qt();return}de(e,this.options,J(s,!1),!1)}}},$(i,{pluginName:"scroll",initializeByDefault:!0})}function qt(){N.forEach(function(i){clearInterval(i.pid)}),N=[]}function ke(){clearInterval(It)}var de=Te(function(i,t,e,n){if(t.scroll){var o=(i.touches?i.touches[0]:i).clientX,r=(i.touches?i.touches[0]:i).clientY,a=t.scrollSensitivity,s=t.scrollSpeed,l=j(),u=!1,f;se!==e&&(se=e,qt(),Tt=t.scroll,f=t.scrollFn,Tt===!0&&(Tt=J(e,!0)));var d=0,y=Tt;do{var _=y,E=I(_),D=E.top,G=E.bottom,K=E.left,A=E.right,H=E.width,R=E.height,ut=void 0,U=void 0,ct=_.scrollWidth,Nt=_.scrollHeight,k=h(_),Ot=_.scrollLeft,nt=_.scrollTop;_===l?(ut=H<ct&&(k.overflowX==="auto"||k.overflowX==="scroll"||k.overflowX==="visible"),U=R<Nt&&(k.overflowY==="auto"||k.overflowY==="scroll"||k.overflowY==="visible")):(ut=H<ct&&(k.overflowX==="auto"||k.overflowX==="scroll"),U=R<Nt&&(k.overflowY==="auto"||k.overflowY==="scroll"));var xt=ut&&(Math.abs(A-o)<=a&&Ot+H<ct)-(Math.abs(K-o)<=a&&!!Ot),Z=U&&(Math.abs(G-r)<=a&&nt+R<Nt)-(Math.abs(D-r)<=a&&!!nt);if(!N[d])for(var dt=0;dt<=d;dt++)N[dt]||(N[dt]={});(N[d].vx!=xt||N[d].vy!=Z||N[d].el!==_)&&(N[d].el=_,N[d].vx=xt,N[d].vy=Z,clearInterval(N[d].pid),(xt!=0||Z!=0)&&(u=!0,N[d].pid=setInterval((function(){n&&this.layer===0&&m.active._onTouchMove($t);var Pt=N[this.layer].vy?N[this.layer].vy*s:0,it=N[this.layer].vx?N[this.layer].vx*s:0;typeof f=="function"&&f.call(m.dragged.parentNode[z],it,Pt,i,$t,N[this.layer].el)!=="continue"||Ie(N[this.layer].el,it,Pt)}).bind({layer:d}),24))),d++}while(t.bubbleScroll&&y!==l&&(y=J(y,!1)));le=u}},30),Le=function(t){var e=t.originalEvent,n=t.putSortable,o=t.dragEl,r=t.activeSortable,a=t.dispatchSortableEvent,s=t.hideGhostForTarget,l=t.unhideGhostForTarget;if(e){var u=n||r;s();var f=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,d=document.elementFromPoint(f.clientX,f.clientY);l(),u&&!u.el.contains(d)&&(a("spill"),this.onSpill({dragEl:o,putSortable:n}))}};function fe(){}fe.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){var e=t.dragEl,n=t.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var o=ht(this.sortable.el,this.startIndex,this.options);o?this.sortable.el.insertBefore(e,o):this.sortable.el.appendChild(e),this.sortable.animateAll(),n&&n.animateAll()},drop:Le},$(fe,{pluginName:"revertOnSpill"});function he(){}he.prototype={onSpill:function(t){var e=t.dragEl,n=t.putSortable,o=n||this.sortable;o.captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),o.animateAll()},drop:Le},$(he,{pluginName:"removeOnSpill"}),m.mount(new pn),m.mount(he,fe);class vn extends Ge{oninit(t){super.oninit(t),this.loading=!1,this.buttonsCustomizationList=[],this.loadResults()}initSort(){const t=document.getElementById("buttonsCustomizationSortableItems");t&&m.create(t,{animation:150,swapThreshold:.65,onEnd:e=>this.updateSort(e)})}content(t){const e=w||p.m||window.m;return e?e("div",{className:"ExtensionPage-settings FlarumBadgesPage"},[e("div",{className:"container"},[e("div",{style:"padding-bottom:10px"},[e(ot,{className:"Button",onclick:()=>p.modal.show(zt,{onSave:()=>this.refreshData()})},p.translator.trans("client1-buttons-customization.admin.link-add"))]),e("ul",{id:"buttonsCustomizationSortableItems",style:"padding:0px;list-style-type: none;",oncreate:this.initSort.bind(this)},this.buttonsCustomizationList.map(n=>e("li",{"data-item-id":n.id(),style:"margin-top:5px;background: var(--body-bg);"},e(We,{ButtonsCustomizationItemData:n,onSave:()=>this.refreshData()}))))])]):"Error: Mithril not loaded"}updateSort(t){const e=t.newIndex,n=t.oldIndex;if(e!==n){const o=t.from?.children,r={};if(o){for(let a=0;a<o.length;a++){const l=o[a].getAttribute("data-item-id");l&&(r[l]=a)}p.request({url:`${p.forum.attribute("apiUrl")}/buttonsCustomizationList/order`,method:"POST",body:{buttonsCustomizationOrder:r}})}}}parseResults(t){return this.buttonsCustomizationList.push(...t),p&&p.m&&p.m.redraw?p.m.redraw():w&&w.redraw&&w.redraw(),t}loadResults(){return p.store.find("buttonsCustomizationList").catch(()=>[]).then(this.parseResults.bind(this))}refreshData(){this.buttonsCustomizationList=[],p.store.data.buttonsCustomizationList={},this.loadResults().then(()=>{p&&p.m&&p.m.redraw?p.m.redraw():w&&w.redraw&&w.redraw()})}}class Xe extends rt{}Object.assign(Xe.prototype,{id:rt.attribute("id"),name:rt.attribute("name"),icon:rt.attribute("icon"),color:rt.attribute("color"),url:rt.attribute("url"),sort:rt.attribute("sort")}),p.initializers.add("wusong8899-buttons-customization",()=>{p.store.models.buttonsCustomizationList=Xe,p.extensionData.for("wusong8899-client1-buttons-customization").registerPage(vn)})})(flarum.core.compat["admin/app"],flarum.core.compat["admin/components/ExtensionPage"],flarum.core.compat["common/components/Button"],flarum.core.compat["common/components/Modal"],flarum.core.compat["common/utils/Stream"],flarum.core.compat.mithril,flarum.core.compat["common/Component"],flarum.core.compat["common/Model"]);
//# sourceMappingURL=admin.js.map

module.exports={};